#!/usr/bin/env python3
"""
PipeWire Virtual Device Manager
動的に仮想オーディオデバイスを作成・管理するツール
"""

import subprocess
import json
import sys
import argparse
from typing import Dict, List, Optional
import re

class PipeWireDeviceManager:
    def __init__(self):
        self.virtual_devices = {}
        self.load_existing_devices()
    
    def run_command(self, cmd: List[str]) -> str:
        """コマンドを実行して結果を返す"""
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Error running command {' '.join(cmd)}: {e.stderr}")
            return ""
    
    def load_existing_devices(self):
        """既存の仮想デバイスを読み込み"""
        output = self.run_command(['wpctl', 'status'])
        # 既存の仮想デバイスを検出するロジック
        # (実装簡略化のため基本的な検出のみ)
        pass
    
    def create_virtual_sink(self, name: str, description: str = None, channels: int = 2) -> bool:
        """仮想シンクを作成"""
        if not description:
            description = f"Virtual Sink: {name}"
        
        # pactl経由での作成
        cmd = [
            'pactl', 'load-module', 'module-null-sink',
            f'sink_name={name}',
            f'sink_properties=device.description="{description}"'
        ]
        
        result = self.run_command(cmd)
        if result:
            self.virtual_devices[name] = {
                'type': 'sink',
                'description': description,
                'module_id': result,
                'channels': channels
            }
            print(f"✓ Virtual sink '{name}' created successfully")
            return True
        return False
    
    def create_virtual_source(self, name: str, description: str = None, channels: int = 2) -> bool:
        """仮想ソースを作成"""
        if not description:
            description = f"Virtual Source: {name}"
        
        cmd = [
            'pactl', 'load-module', 'module-null-source',
            f'source_name={name}',
            f'source_properties=device.description="{description}"'
        ]
        
        result = self.run_command(cmd)
        if result:
            self.virtual_devices[name] = {
                'type': 'source',
                'description': description,
                'module_id': result,
                'channels': channels
            }
            print(f"✓ Virtual source '{name}' created successfully")
            return True
        return False
    
    def create_loopback(self, source_name: str, sink_name: str, loop_name: str = None) -> bool:
        """ループバック接続を作成"""
        if not loop_name:
            loop_name = f"{source_name}_to_{sink_name}"
        
        cmd = [
            'pactl', 'load-module', 'module-loopback',
            f'source={source_name}',
            f'sink={sink_name}'
        ]
        
        result = self.run_command(cmd)
        if result:
            self.virtual_devices[loop_name] = {
                'type': 'loopback',
                'source': source_name,
                'sink': sink_name,
                'module_id': result
            }
            print(f"✓ Loopback '{loop_name}' created: {source_name} -> {sink_name}")
            return True
        return False
    
    def remove_device(self, name: str) -> bool:
        """仮想デバイスを削除"""
        if name not in self.virtual_devices:
            print(f"Device '{name}' not found")
            return False
        
        device = self.virtual_devices[name]
        cmd = ['pactl', 'unload-module', device['module_id']]
        
        result = self.run_command(cmd)
        del self.virtual_devices[name]
        print(f"✓ Device '{name}' removed")
        return True
    
    def list_devices(self):
        """仮想デバイス一覧を表示"""
        if not self.virtual_devices:
            print("No virtual devices found")
            return
        
        print("\n=== Virtual Devices ===")
        for name, info in self.virtual_devices.items():
            print(f"• {name} ({info['type']})")
            print(f"  Description: {info['description']}")
            print(f"  Module ID: {info['module_id']}")
            print()
    
    def get_system_status(self):
        """システムの音声デバイス状況を表示"""
        print("\n=== System Audio Status ===")
        output = self.run_command(['wpctl', 'status'])
        
        # シンクセクションを抽出
        lines = output.split('\n')
        in_sinks = False
        in_sources = False
        
        for line in lines:
            if '├─ Sinks:' in line:
                in_sinks = True
                in_sources = False
                print("\n📢 Audio Sinks:")
                continue
            elif '├─ Sources:' in line:
                in_sinks = False
                in_sources = True
                print("\n🎤 Audio Sources:")
                continue
            elif '├─' in line and ('Filters' in line or 'Streams' in line):
                in_sinks = False
                in_sources = False
                continue
            
            if (in_sinks or in_sources) and ('│' in line):
                # デバイス行を整形して表示
                device_line = line.strip()
                if device_line.startswith('│'):
                    print(f"  {device_line}")
    
    def connect_devices(self, source: str, sink: str):
        """デバイス間を接続"""
        cmd = ['pw-link', f"{source}:monitor_FL", f"{sink}:playback_FL"]
        self.run_command(cmd)
        cmd = ['pw-link', f"{source}:monitor_FR", f"{sink}:playback_FR"]
        self.run_command(cmd)
        print(f"✓ Connected {source} -> {sink}")
    
    def save_config(self, filename: str):
        """現在の設定をファイルに保存"""
        config = {
            'virtual_devices': self.virtual_devices
        }
        with open(filename, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"✓ Configuration saved to {filename}")
    
    def load_config(self, filename: str):
        """設定ファイルから復元"""
        try:
            with open(filename, 'r') as f:
                config = json.load(f)
            
            for name, info in config['virtual_devices'].items():
                if info['type'] == 'sink':
                    self.create_virtual_sink(name, info['description'])
                elif info['type'] == 'source':
                    self.create_virtual_source(name, info['description'])
                elif info['type'] == 'loopback':
                    self.create_loopback(info['source'], info['sink'], name)
            
            print(f"✓ Configuration loaded from {filename}")
        except FileNotFoundError:
            print(f"Configuration file {filename} not found")

def main():
    parser = argparse.ArgumentParser(description='PipeWire Virtual Device Manager')
    parser.add_argument('command', choices=[
        'list', 'status', 'create-sink', 'create-source', 'create-loop', 
        'remove', 'connect', 'save', 'load'
    ])
    parser.add_argument('--name', help='Device name')
    parser.add_argument('--description', help='Device description')
    parser.add_argument('--source', help='Source device for connection')
    parser.add_argument('--sink', help='Sink device for connection')
    parser.add_argument('--file', help='Configuration file')
    parser.add_argument('--channels', type=int, default=2, help='Number of channels')
    
    args = parser.parse_args()
    
    manager = PipeWireDeviceManager()
    
    if args.command == 'list':
        manager.list_devices()
    
    elif args.command == 'status':
        manager.get_system_status()
    
    elif args.command == 'create-sink':
        if not args.name:
            print("Error: --name is required for create-sink")
            sys.exit(1)
        manager.create_virtual_sink(args.name, args.description, args.channels)
    
    elif args.command == 'create-source':
        if not args.name:
            print("Error: --name is required for create-source")
            sys.exit(1)
        manager.create_virtual_source(args.name, args.description, args.channels)
    
    elif args.command == 'create-loop':
        if not args.source or not args.sink:
            print("Error: --source and --sink are required for create-loop")
            sys.exit(1)
        manager.create_loopback(args.source, args.sink, args.name)
    
    elif args.command == 'remove':
        if not args.name:
            print("Error: --name is required for remove")
            sys.exit(1)
        manager.remove_device(args.name)
    
    elif args.command == 'connect':
        if not args.source or not args.sink:
            print("Error: --source and --sink are required for connect")
            sys.exit(1)
        manager.connect_devices(args.source, args.sink)
    
    elif args.command == 'save':
        if not args.file:
            print("Error: --file is required for save")
            sys.exit(1)
        manager.save_config(args.file)
    
    elif args.command == 'load':
        if not args.file:
            print("Error: --file is required for load")
            sys.exit(1)
        manager.load_config(args.file)

if __name__ == '__main__':
    main()