{
    gcc_arch_x86_64_plat_linux = {
        __checked = {
            program = "/run/current-system/sw/bin/gcc",
            name = "gcc"
        },
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    fpc_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    swift_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    nasm_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    cross_arch_x86_64_plat_linux = {
        __checked = false,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    yasm_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    nim_arch_x86_64_plat_linux = {
        __checked = false,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    cuda_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    fasm_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    go_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    gfortran_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    rust_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    },
    envs_arch_x86_64_plat_linux = {
        __checked = true,
        plat = "linux",
        __global = true,
        arch = "x86_64"
    }
}