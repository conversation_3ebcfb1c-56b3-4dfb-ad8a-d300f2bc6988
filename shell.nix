{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    xmake
    cmake
    ninja
    pkg-config
    
    # Qt6 development
    qt6.full
    qt6.qtbase
    qt6.qttools
    qt6.qmake
    
    # C++ compiler and tools
    gcc
    clang
    gdb
    valgrind
    
    # PipeWire and audio tools
    pipewire
    wireplumber
    pulseaudio
    
    # Development utilities
    git
    vim
    tree
    htop
  ];

  shellHook = ''
    echo "🎵 PipeWire Virtual Device Manager Development Environment"
    echo "=================================================="
    echo "Available tools:"
    echo "  - xmake: Build system"
    echo "  - Qt6: GUI framework"
    echo "  - PipeWire: Audio system"
    echo ""
    echo "Quick start:"
    echo "  xmake config --mode=debug"
    echo "  xmake build"
    echo "  xmake run"
    echo ""
    
    # Set Qt6 environment variables
    export QT_SELECT=6
    export QT6_DIR=${pkgs.qt6.qtbase}
    
    # Add Qt6 tools to PATH
    export PATH="${pkgs.qt6.qtbase}/bin:${pkgs.qt6.qttools}/bin:$PATH"
    
    # Set PKG_CONFIG_PATH for Qt6
    export PKG_CONFIG_PATH="${pkgs.qt6.qtbase}/lib/pkgconfig:$PKG_CONFIG_PATH"
  '';
}
