#include "pipewire_device_manager.h"
#include <QCoreApplication>
#include <QTimer>
#include <QEventLoop>

// VirtualDevice implementation
QJsonObject VirtualDevice::toJson() const {
    QJsonObject obj;
    obj["type"] = type;
    obj["description"] = description;
    obj["module_id"] = moduleId;
    obj["channels"] = channels;
    if (!source.isEmpty()) obj["source"] = source;
    if (!sink.isEmpty()) obj["sink"] = sink;
    return obj;
}

VirtualDevice VirtualDevice::fromJson(const QJsonObject& json) {
    VirtualDevice device;
    device.type = json["type"].toString();
    device.description = json["description"].toString();
    device.moduleId = json["module_id"].toString();
    device.channels = json["channels"].toInt(2);
    device.source = json["source"].toString();
    device.sink = json["sink"].toString();
    return device;
}

// PipeWireDeviceManager implementation
PipeWireDeviceManager::PipeWireDeviceManager(QObject *parent)
    : QObject(parent)
    , m_currentProcess(nullptr)
    , m_commandSuccess(false)
{
    loadExistingDevices();
}

PipeWireDeviceManager::~PipeWireDeviceManager()
{
    if (m_currentProcess) {
        m_currentProcess->kill();
        m_currentProcess->waitForFinished(3000);
        delete m_currentProcess;
    }
}

QString PipeWireDeviceManager::runCommand(const QStringList& command)
{
    if (command.isEmpty()) {
        printError("Empty command provided");
        return QString();
    }

    m_currentProcess = new QProcess(this);
    
    // Connect signals
    connect(m_currentProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &PipeWireDeviceManager::onProcessFinished);
    connect(m_currentProcess, &QProcess::errorOccurred,
            this, &PipeWireDeviceManager::onProcessError);

    // Start the process
    QString program = command.first();
    QStringList arguments = command.mid(1);
    
    m_currentProcess->start(program, arguments);
    
    // Wait for the process to finish
    QEventLoop loop;
    connect(m_currentProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            &loop, &QEventLoop::quit);
    
    if (!m_currentProcess->waitForStarted(5000)) {
        printError(QString("Failed to start command: %1").arg(command.join(" ")));
        delete m_currentProcess;
        m_currentProcess = nullptr;
        return QString();
    }
    
    loop.exec();
    
    QString output = m_lastCommandOutput;
    bool success = m_commandSuccess;
    
    delete m_currentProcess;
    m_currentProcess = nullptr;
    
    if (!success) {
        printError(QString("Command failed: %1").arg(command.join(" ")));
        return QString();
    }
    
    return output.trimmed();
}

void PipeWireDeviceManager::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    if (exitStatus == QProcess::NormalExit && exitCode == 0) {
        m_lastCommandOutput = m_currentProcess->readAllStandardOutput();
        m_commandSuccess = true;
    } else {
        m_lastCommandOutput = m_currentProcess->readAllStandardError();
        m_commandSuccess = false;
    }
}

void PipeWireDeviceManager::onProcessError(QProcess::ProcessError error)
{
    Q_UNUSED(error)
    m_lastCommandOutput = m_currentProcess->errorString();
    m_commandSuccess = false;
}

void PipeWireDeviceManager::loadExistingDevices()
{
    // For now, we'll start with an empty device list
    // In a full implementation, we would parse wpctl status output
    // to detect existing virtual devices
    m_virtualDevices.clear();
}

bool PipeWireDeviceManager::createVirtualSink(const QString& name, const QString& description, int channels)
{
    if (name.isEmpty()) {
        printError("Device name cannot be empty");
        return false;
    }
    
    if (m_virtualDevices.contains(name)) {
        printError(QString("Device '%1' already exists").arg(name));
        return false;
    }
    
    QString desc = description.isEmpty() ? QString("Virtual Sink: %1").arg(name) : description;
    
    QStringList cmd = {
        "pactl", "load-module", "module-null-sink",
        QString("sink_name=%1").arg(name),
        QString("sink_properties=device.description=\"%1\"").arg(desc)
    };
    
    QString result = runCommand(cmd);
    if (!result.isEmpty()) {
        VirtualDevice device;
        device.type = "sink";
        device.description = desc;
        device.moduleId = result;
        device.channels = channels;
        
        m_virtualDevices[name] = device;
        printSuccess(QString("Virtual sink '%1' created successfully").arg(name));
        return true;
    }
    
    return false;
}

bool PipeWireDeviceManager::createVirtualSource(const QString& name, const QString& description, int channels)
{
    if (name.isEmpty()) {
        printError("Device name cannot be empty");
        return false;
    }
    
    if (m_virtualDevices.contains(name)) {
        printError(QString("Device '%1' already exists").arg(name));
        return false;
    }
    
    QString desc = description.isEmpty() ? QString("Virtual Source: %1").arg(name) : description;
    
    QStringList cmd = {
        "pactl", "load-module", "module-null-source",
        QString("source_name=%1").arg(name),
        QString("source_properties=device.description=\"%1\"").arg(desc)
    };
    
    QString result = runCommand(cmd);
    if (!result.isEmpty()) {
        VirtualDevice device;
        device.type = "source";
        device.description = desc;
        device.moduleId = result;
        device.channels = channels;
        
        m_virtualDevices[name] = device;
        printSuccess(QString("Virtual source '%1' created successfully").arg(name));
        return true;
    }
    
    return false;
}

void PipeWireDeviceManager::printSuccess(const QString& message) const
{
    qDebug() << "✓" << message;
}

void PipeWireDeviceManager::printError(const QString& message) const
{
    qDebug() << "✗" << message;
}

void PipeWireDeviceManager::printInfo(const QString& message) const
{
    qDebug() << "ℹ" << message;
}

bool PipeWireDeviceManager::createLoopback(const QString& sourceName, const QString& sinkName, const QString& loopName)
{
    if (sourceName.isEmpty() || sinkName.isEmpty()) {
        printError("Source and sink names cannot be empty");
        return false;
    }

    QString name = loopName.isEmpty() ? QString("%1_to_%2").arg(sourceName, sinkName) : loopName;

    if (m_virtualDevices.contains(name)) {
        printError(QString("Loopback '%1' already exists").arg(name));
        return false;
    }

    QStringList cmd = {
        "pactl", "load-module", "module-loopback",
        QString("source=%1").arg(sourceName),
        QString("sink=%1").arg(sinkName)
    };

    QString result = runCommand(cmd);
    if (!result.isEmpty()) {
        VirtualDevice device;
        device.type = "loopback";
        device.source = sourceName;
        device.sink = sinkName;
        device.moduleId = result;
        device.channels = 2;
        device.description = QString("Loopback: %1 -> %2").arg(sourceName, sinkName);

        m_virtualDevices[name] = device;
        printSuccess(QString("Loopback '%1' created: %2 -> %3").arg(name, sourceName, sinkName));
        return true;
    }

    return false;
}

bool PipeWireDeviceManager::removeDevice(const QString& name)
{
    if (!m_virtualDevices.contains(name)) {
        printError(QString("Device '%1' not found").arg(name));
        return false;
    }

    const VirtualDevice& device = m_virtualDevices[name];
    QStringList cmd = {"pactl", "unload-module", device.moduleId};

    QString result = runCommand(cmd);
    if (m_commandSuccess) {
        m_virtualDevices.remove(name);
        printSuccess(QString("Device '%1' removed").arg(name));
        return true;
    }

    return false;
}

void PipeWireDeviceManager::listDevices() const
{
    if (m_virtualDevices.isEmpty()) {
        printInfo("No virtual devices found");
        return;
    }

    printInfo("\n=== Virtual Devices ===");
    for (auto it = m_virtualDevices.constBegin(); it != m_virtualDevices.constEnd(); ++it) {
        const QString& name = it.key();
        const VirtualDevice& info = it.value();

        qDebug() << QString("• %1 (%2)").arg(name, info.type);
        qDebug() << QString("  Description: %1").arg(info.description);
        qDebug() << QString("  Module ID: %1").arg(info.moduleId);
        if (info.type == "loopback") {
            qDebug() << QString("  Connection: %1 -> %2").arg(info.source, info.sink);
        }
        qDebug() << "";
    }
}

void PipeWireDeviceManager::getSystemStatus() const
{
    printInfo("\n=== System Audio Status ===");
    QString output = const_cast<PipeWireDeviceManager*>(this)->runCommand({"wpctl", "status"});

    if (output.isEmpty()) {
        printError("Failed to get system status");
        return;
    }

    QStringList lines = output.split('\n');
    bool inSinks = false;
    bool inSources = false;

    for (const QString& line : lines) {
        if (line.contains("├─ Sinks:")) {
            inSinks = true;
            inSources = false;
            qDebug() << "\n📢 Audio Sinks:";
            continue;
        } else if (line.contains("├─ Sources:")) {
            inSinks = false;
            inSources = true;
            qDebug() << "\n🎤 Audio Sources:";
            continue;
        } else if (line.contains("├─") && (line.contains("Filters") || line.contains("Streams"))) {
            inSinks = false;
            inSources = false;
            continue;
        }

        if ((inSinks || inSources) && line.contains("│")) {
            QString deviceLine = line.trimmed();
            if (deviceLine.startsWith("│")) {
                qDebug() << QString("  %1").arg(deviceLine);
            }
        }
    }
}

bool PipeWireDeviceManager::connectDevices(const QString& source, const QString& sink)
{
    if (source.isEmpty() || sink.isEmpty()) {
        printError("Source and sink names cannot be empty");
        return false;
    }

    // Connect left channel
    QStringList cmd1 = {"pw-link", QString("%1:monitor_FL").arg(source), QString("%1:playback_FL").arg(sink)};
    QString result1 = runCommand(cmd1);

    // Connect right channel
    QStringList cmd2 = {"pw-link", QString("%1:monitor_FR").arg(source), QString("%1:playback_FR").arg(sink)};
    QString result2 = runCommand(cmd2);

    if (m_commandSuccess) {
        printSuccess(QString("Connected %1 -> %2").arg(source, sink));
        return true;
    }

    return false;
}

bool PipeWireDeviceManager::saveConfig(const QString& filename) const
{
    if (filename.isEmpty()) {
        printError("Filename cannot be empty");
        return false;
    }

    QJsonObject config;
    QJsonObject virtualDevicesObj;

    for (auto it = m_virtualDevices.constBegin(); it != m_virtualDevices.constEnd(); ++it) {
        virtualDevicesObj[it.key()] = it.value().toJson();
    }

    config["virtual_devices"] = virtualDevicesObj;

    QJsonDocument doc(config);
    QFile file(filename);

    if (!file.open(QIODevice::WriteOnly)) {
        printError(QString("Failed to open file for writing: %1").arg(filename));
        return false;
    }

    file.write(doc.toJson());
    file.close();

    printSuccess(QString("Configuration saved to %1").arg(filename));
    return true;
}

bool PipeWireDeviceManager::loadConfig(const QString& filename)
{
    if (filename.isEmpty()) {
        printError("Filename cannot be empty");
        return false;
    }

    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        printError(QString("Configuration file %1 not found").arg(filename));
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        printError(QString("Failed to parse JSON: %1").arg(error.errorString()));
        return false;
    }

    QJsonObject config = doc.object();
    QJsonObject virtualDevicesObj = config["virtual_devices"].toObject();

    for (auto it = virtualDevicesObj.constBegin(); it != virtualDevicesObj.constEnd(); ++it) {
        const QString& name = it.key();
        VirtualDevice device = VirtualDevice::fromJson(it.value().toObject());

        if (device.type == "sink") {
            createVirtualSink(name, device.description, device.channels);
        } else if (device.type == "source") {
            createVirtualSource(name, device.description, device.channels);
        } else if (device.type == "loopback") {
            createLoopback(device.source, device.sink, name);
        }
    }

    printSuccess(QString("Configuration loaded from %1").arg(filename));
    return true;
}
