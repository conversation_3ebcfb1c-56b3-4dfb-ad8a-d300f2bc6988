#ifndef PIPEWIRE_DEVICE_MANAGER_H
#define PIPEWIRE_DEVICE_MANAGER_H

#include <QObject>
#include <QProcess>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QMap>
#include <QString>
#include <QStringList>
#include <QTextStream>
#include <QDebug>
#include <QFile>
#include <QIODevice>

/**
 * @brief Virtual device information structure
 */
struct VirtualDevice {
    QString type;           // "sink", "source", or "loopback"
    QString description;    // Human-readable description
    QString moduleId;       // PulseAudio module ID
    int channels;           // Number of audio channels
    QString source;         // For loopback: source device name
    QString sink;           // For loopback: sink device name
    
    // Convert to/from JSON
    QJsonObject toJson() const;
    static VirtualDevice fromJson(const QJsonObject& json);
};

/**
 * @brief PipeWire Virtual Device Manager
 * 
 * This class provides functionality to create, manage, and remove
 * virtual audio devices in PipeWire/PulseAudio systems.
 */
class PipeWireDeviceManager : public QObject
{
    Q_OBJECT

public:
    explicit PipeWireDeviceManager(QObject *parent = nullptr);
    ~PipeWireDeviceManager();

    // Device creation methods
    bool createVirtualSink(const QString& name, const QString& description = QString(), int channels = 2);
    bool createVirtualSource(const QString& name, const QString& description = QString(), int channels = 2);
    bool createLoopback(const QString& sourceName, const QString& sinkName, const QString& loopName = QString());
    
    // Device management
    bool removeDevice(const QString& name);
    void listDevices() const;
    void getSystemStatus() const;
    bool connectDevices(const QString& source, const QString& sink);
    
    // Configuration management
    bool saveConfig(const QString& filename) const;
    bool loadConfig(const QString& filename);
    
    // Getters
    QMap<QString, VirtualDevice> getVirtualDevices() const { return m_virtualDevices; }
    bool hasDevice(const QString& name) const { return m_virtualDevices.contains(name); }

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessError(QProcess::ProcessError error);

private:
    // Internal methods
    QString runCommand(const QStringList& command);
    void loadExistingDevices();
    void printSuccess(const QString& message) const;
    void printError(const QString& message) const;
    void printInfo(const QString& message) const;
    
    // Member variables
    QMap<QString, VirtualDevice> m_virtualDevices;
    QProcess* m_currentProcess;
    QString m_lastCommandOutput;
    bool m_commandSuccess;
};

#endif // PIPEWIRE_DEVICE_MANAGER_H
