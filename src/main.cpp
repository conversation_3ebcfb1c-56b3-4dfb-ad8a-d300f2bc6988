#include <QCoreApplication>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QDebug>
#include <QTextStream>
#include "pipewire_device_manager.h"

void printUsage(const QString& programName) {
    QTextStream out(stdout);
    out << "PipeWire Virtual Device Manager\n";
    out << "動的に仮想オーディオデバイスを作成・管理するツール\n\n";
    out << "Usage: " << programName << " <command> [options]\n\n";
    out << "Commands:\n";
    out << "  list              List all virtual devices\n";
    out << "  status            Show system audio status\n";
    out << "  create-sink       Create a virtual sink\n";
    out << "  create-source     Create a virtual source\n";
    out << "  create-loop       Create a loopback connection\n";
    out << "  remove            Remove a virtual device\n";
    out << "  connect           Connect devices\n";
    out << "  save              Save configuration to file\n";
    out << "  load              Load configuration from file\n\n";
    out << "Options:\n";
    out << "  --name <name>         Device name\n";
    out << "  --description <desc>  Device description\n";
    out << "  --source <source>     Source device for connection\n";
    out << "  --sink <sink>         Sink device for connection\n";
    out << "  --file <file>         Configuration file\n";
    out << "  --channels <num>      Number of channels (default: 2)\n";
    out << "  --help                Show this help\n";
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    app.setApplicationName("pipewire-vdm");
    app.setApplicationVersion("1.0.0");
    app.setApplicationDisplayName("PipeWire Virtual Device Manager");

    QCommandLineParser parser;
    parser.setApplicationDescription("PipeWire Virtual Device Manager - 動的に仮想オーディオデバイスを作成・管理するツール");
    
    // Add positional argument for command
    parser.addPositionalArgument("command", "Command to execute (list, status, create-sink, create-source, create-loop, remove, connect, save, load)");
    
    // Add options
    QCommandLineOption nameOption(QStringList() << "name", "Device name", "name");
    parser.addOption(nameOption);
    
    QCommandLineOption descriptionOption(QStringList() << "description", "Device description", "description");
    parser.addOption(descriptionOption);
    
    QCommandLineOption sourceOption(QStringList() << "source", "Source device for connection", "source");
    parser.addOption(sourceOption);
    
    QCommandLineOption sinkOption(QStringList() << "sink", "Sink device for connection", "sink");
    parser.addOption(sinkOption);
    
    QCommandLineOption fileOption(QStringList() << "file", "Configuration file", "file");
    parser.addOption(fileOption);
    
    QCommandLineOption channelsOption(QStringList() << "channels", "Number of channels", "channels", "2");
    parser.addOption(channelsOption);
    
    // Add help option
    parser.addHelpOption();
    parser.addVersionOption();

    // Parse command line
    parser.process(app);

    const QStringList args = parser.positionalArguments();
    if (args.isEmpty()) {
        printUsage(app.applicationName());
        return 1;
    }

    QString command = args.first();
    PipeWireDeviceManager manager;

    // Execute commands
    if (command == "list") {
        manager.listDevices();
    }
    else if (command == "status") {
        manager.getSystemStatus();
    }
    else if (command == "create-sink") {
        if (!parser.isSet(nameOption)) {
            qDebug() << "Error: --name is required for create-sink";
            return 1;
        }
        QString name = parser.value(nameOption);
        QString description = parser.value(descriptionOption);
        int channels = parser.value(channelsOption).toInt();
        
        if (!manager.createVirtualSink(name, description, channels)) {
            return 1;
        }
    }
    else if (command == "create-source") {
        if (!parser.isSet(nameOption)) {
            qDebug() << "Error: --name is required for create-source";
            return 1;
        }
        QString name = parser.value(nameOption);
        QString description = parser.value(descriptionOption);
        int channels = parser.value(channelsOption).toInt();
        
        if (!manager.createVirtualSource(name, description, channels)) {
            return 1;
        }
    }
    else if (command == "create-loop") {
        if (!parser.isSet(sourceOption) || !parser.isSet(sinkOption)) {
            qDebug() << "Error: --source and --sink are required for create-loop";
            return 1;
        }
        QString source = parser.value(sourceOption);
        QString sink = parser.value(sinkOption);
        QString name = parser.value(nameOption);
        
        if (!manager.createLoopback(source, sink, name)) {
            return 1;
        }
    }
    else if (command == "remove") {
        if (!parser.isSet(nameOption)) {
            qDebug() << "Error: --name is required for remove";
            return 1;
        }
        QString name = parser.value(nameOption);
        
        if (!manager.removeDevice(name)) {
            return 1;
        }
    }
    else if (command == "connect") {
        if (!parser.isSet(sourceOption) || !parser.isSet(sinkOption)) {
            qDebug() << "Error: --source and --sink are required for connect";
            return 1;
        }
        QString source = parser.value(sourceOption);
        QString sink = parser.value(sinkOption);
        
        if (!manager.connectDevices(source, sink)) {
            return 1;
        }
    }
    else if (command == "save") {
        if (!parser.isSet(fileOption)) {
            qDebug() << "Error: --file is required for save";
            return 1;
        }
        QString file = parser.value(fileOption);
        
        if (!manager.saveConfig(file)) {
            return 1;
        }
    }
    else if (command == "load") {
        if (!parser.isSet(fileOption)) {
            qDebug() << "Error: --file is required for load";
            return 1;
        }
        QString file = parser.value(fileOption);
        
        if (!manager.loadConfig(file)) {
            return 1;
        }
    }
    else {
        qDebug() << "Error: Unknown command:" << command;
        printUsage(app.applicationName());
        return 1;
    }

    return 0;
}
