# PipeWire Virtual Device Manager

動的に仮想オーディオデバイスを作成・管理するC++/Qt6ツール

## 概要

このプロジェクトは、PipeWire/PulseAudioシステムで仮想オーディオデバイスを動的に作成・管理するためのコマンドラインツールです。元のPythonバージョンをC++とQt6で再実装しました。

## 機能

- 仮想シンク（出力デバイス）の作成
- 仮想ソース（入力デバイス）の作成
- ループバック接続の作成
- デバイスの削除
- デバイス一覧表示
- システム音声状況の表示
- 設定の保存・読み込み

## 必要な環境

- C++17対応コンパイラ
- Qt6 (Core, Widgets)
- xmake ビルドシステム
- PipeWire/PulseAudio
- Nix (開発環境用)

## ビルド方法

### Nixを使用する場合

```bash
# 開発環境に入る
nix-shell

# プロジェクトをビルド
xmake config --mode=debug
xmake build

# 実行
xmake run
```

### 手動ビルド

```bash
# 依存関係をインストール（Ubuntu/Debian）
sudo apt install qt6-base-dev qt6-tools-dev build-essential

# xmakeをインストール
curl -fsSL https://xmake.io/shget.text | bash

# ビルド
xmake config --mode=debug
xmake build
```

## 使用方法

```bash
# ヘルプを表示
./build/pipewire-vdm --help

# 仮想シンクを作成
./build/pipewire-vdm create-sink --name my_sink --description "My Virtual Sink"

# 仮想ソースを作成
./build/pipewire-vdm create-source --name my_source --description "My Virtual Source"

# ループバック接続を作成
./build/pipewire-vdm create-loop --source my_source --sink my_sink

# デバイス一覧を表示
./build/pipewire-vdm list

# システム状況を表示
./build/pipewire-vdm status

# デバイスを削除
./build/pipewire-vdm remove --name my_sink

# 設定を保存
./build/pipewire-vdm save --file config.json

# 設定を読み込み
./build/pipewire-vdm load --file config.json
```

## コマンド一覧

| コマンド | 説明 | 必須オプション |
|----------|------|----------------|
| `list` | 仮想デバイス一覧を表示 | なし |
| `status` | システム音声状況を表示 | なし |
| `create-sink` | 仮想シンクを作成 | `--name` |
| `create-source` | 仮想ソースを作成 | `--name` |
| `create-loop` | ループバック接続を作成 | `--source`, `--sink` |
| `remove` | 仮想デバイスを削除 | `--name` |
| `connect` | デバイス間を接続 | `--source`, `--sink` |
| `save` | 設定をファイルに保存 | `--file` |
| `load` | 設定ファイルから復元 | `--file` |

## オプション

- `--name <name>`: デバイス名
- `--description <desc>`: デバイスの説明
- `--source <source>`: 接続用ソースデバイス
- `--sink <sink>`: 接続用シンクデバイス
- `--file <file>`: 設定ファイル
- `--channels <num>`: チャンネル数（デフォルト: 2）

## プロジェクト構造

```
pipewire-vdm/
├── src/
│   ├── main.cpp                    # メインエントリーポイント
│   ├── pipewire_device_manager.h   # デバイスマネージャーヘッダー
│   └── pipewire_device_manager.cpp # デバイスマネージャー実装
├── xmake.lua                       # ビルド設定
├── shell.nix                       # Nix開発環境
└── README.md                       # このファイル
```

## 技術仕様

- **言語**: C++17
- **フレームワーク**: Qt6 Core
- **ビルドシステム**: xmake
- **音声システム**: PipeWire/PulseAudio
- **設定形式**: JSON

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。
