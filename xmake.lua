-- xmake.lua for PipeWire Virtual Device Manager
set_project("pipewire-vdm")
set_version("1.0.0")
set_languages("c++17")

-- Add required packages
add_requires("qt6core", "qt6widgets", {configs = {shared = true}})

-- Define the target
target("pipewire-vdm")
    set_kind("binary")
    
    -- Add source files
    add_files("src/*.cpp")
    add_headerfiles("src/*.h")
    
    -- Add Qt6 packages
    add_packages("qt6core", "qt6widgets")
    
    -- Enable Qt MOC (Meta-Object Compiler)
    add_rules("qt.widgetapp")
    
    -- Set output directory
    set_targetdir("build")
    
    -- Compiler flags
    add_cxxflags("-Wall", "-Wextra", "-std=c++17")
    
    -- Debug configuration
    if is_mode("debug") then
        add_defines("DEBUG")
        set_symbols("debug")
        set_optimize("none")
    end
    
    -- Release configuration
    if is_mode("release") then
        set_symbols("hidden")
        set_optimize("fastest")
        set_strip("all")
    end

-- Add install rules
target("pipewire-vdm")
    add_installfiles("build/pipewire-vdm", {prefixdir = "bin"})
